const { IView } = require('../../../component/iview');
const { SmartTable } = require('../../../libs/table/smart-table');
const { TaskObject } = require('../../20cm-july/objects');
const { Cm20FunctionCodes } = require('../../../config/20cm');

module.exports = class SellTaskView extends IView {

    constructor() {

        super('@20cm-july/main/sell-task', false, '卖出策略');
        this.strategies = [
            { label: '定时定量-买入', value: 98 },
            { label: '定时定量-卖出', value: 101 },
            { label: '低封单量', value: 103 },
            { label: '定时买', value: 109 },
        ];
    }

    /**
     * @param {Array<TaskObject>} tasks
     */
    push(tasks) {
        tasks.forEach(tsk => {
            tsk.strategyType = tsk.boardStrategy.strategyType;
            tsk.strategyVolume = tsk.boardStrategy.strategyVolume;

            if (TaskObject.isTaskDeleted(tsk.strikeBoardStatus)) {
                this.table.deleteRow(this.identify(tsk));
            }
            else {
                this.table.putRow(tsk);
            }
        });
    }

    /**
     * @param {TaskObject} record 
     */
    formatStrategy(record) {

        let stype = record.boardStrategy.strategyType;
        let matched = this.strategies.find(x => x.value == stype);
        return matched ? matched.label : stype;
    }

    /**
     * @param {TaskObject} record 
     */
    formatStatus(record) {

        let status = record.strikeBoardStatus;
        if (TaskObject.isTaskRunning(status)) {
            return '运行中';
        }
        else if (TaskObject.isTaskPaused(status)) {
            return '已停止';
        }
        else if (TaskObject.isTaskCreated(status)) {
            return '已创建';
        } else if (TaskObject.isTaskFinished(status)) {
            return '已完成';
        }
        else {
            return '---';
        }
    }

    /**
     * @param {String} instrument
     */
    shortize(data, instrument) {
        
        let parts = instrument.split('.');
        return parts[1] || parts[0];
    }

    /**
     * @param {TaskObject} record
     */
    formatActions(record) {

        let status = record.strikeBoardStatus;
        if (TaskObject.isTaskRunning(status)) {
            return '<button class="danger" event.onclick="stop">停止</button>';
        }
        else if (TaskObject.isTaskPaused(status) || TaskObject.isTaskCreated(status)) {

            let buttons = ['<button style="background-color: #1BBE48;" event.onclick="start">开始</button>'];
            if (TaskObject.isTaskDeletable(status)) {
                buttons.push(`<button class="danger" event.onclick="remove">删除</button>`);
            }
            
            return buttons.join('');
        } else if (TaskObject.isTaskFinished(status)) {
            return '<button class="danger" event.onclick="remove">删除</button>';
        }
    }

    /**
     * @param {TaskObject} record
     */
    renderDirection(record) {

      const type = record.boardStrategy.strategyType;
      const isBuy = type == 98 || type == 109;
      return `<div style="color: ${isBuy ? 'red' : 'green'};">${isBuy ? '买入' : '卖出'}</div>`;
    }

    /**
     * @param {TaskObject} record
     */
    handleRowDbClick(record) {
        this.trigger('hit-sell-task', record);
    }
    
    /**
     * @param {TaskObject} task 
     */
    start(task) {

        let cloned = this.helper.deepClone(task);
        delete cloned.instrumentName;
        delete cloned.orderInfo;
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.start, cloned, 0);
        this.interaction.showSuccess('已发送【开始】指令');
    }

    /**
     * @param {TaskObject} task 
     */
    stop(task) {

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: task.id });
        this.interaction.showSuccess('已发送【停止】指令');
    }

    /**
     * @param {TaskObject} task 
     */
    remove(task) {

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.delete, { id: task.id });
        this.interaction.showSuccess('已发送【删除】指令');
    }

    /**
     * @param {TaskObject} record 
     */
    identify(record) {
        return record.id;
    }

    createTable($table) {

        this.table = new SmartTable($table, this.identify, this, { 
            tableName: 'smt-yhsc', 
            displayName: this.title,
            rowDbClicked: this.handleRowDbClick.bind(this),
        });

        this.table.setPageSize(9999);
    }

    build($container) {

        super.build($container);
        const $table = this.$container.querySelector('.task-list');
        this.createTable($table);
    }
};