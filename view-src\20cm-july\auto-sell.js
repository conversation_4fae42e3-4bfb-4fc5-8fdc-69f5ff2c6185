const { BaseView } = require('./base-view');
const { algorithmTypeEnum } = require('../../config/auto-sell');
const AlgorithmDialogCom = require('./auto-sell/AlogorithmDialog/index');
const DefaultParamDialogCom = require('./auto-sell/DefaultParamDialog/index');
const AlgorithmTableCom = require('./auto-sell/AlgorithmTable/index');
const { TaskObject, TaskStatus, TaskStatuses } = require('./objects');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { Order } = require('../../model/order');
const { repoInstrument } = require('../../repository/instrument');
const { repo20Cm } = require('../../repository/20cm');
const { SmartTable } = require('../../libs/table/smart-table');
const { repoOrder } = require('../../repository/order');
const { ModelConverter } = require('../../model/model-converter');

/**
 * 一个新的订单，首次发生成交（买入方向）时，需要进行提示（仅提示1次），该map存储数据为：订单id/true，标识为已提示
 */
const GNotifyingMap = {};
module.exports = class TradeView extends BaseView {
  get notifyingMap() {
    return GNotifyingMap;
  }

  constructor(view_name, is_standalone_window) {
    super(view_name, is_standalone_window, '自动卖出');
  }

  async createView() {
    let self = this;
    let AlgorithmDialog = await new AlgorithmDialogCom().build(this);
    let DefaultParamDialog = await new DefaultParamDialogCom().build(this);
    let AlgorithmTable = await new AlgorithmTableCom().build(this);
    this.vueApp = new Vue({
      el: this.$container.querySelector('.auto-sell'),
      components: {
        AlgorithmDialog,
        DefaultParamDialog,
        AlgorithmTable,
      },
      data: {
        showView: false,
        priceMap: {},
        algorithmDialog: {
          visible: false,
          item: null,
        },
        defaultParamDialog: {
          visible: false,
        },
        remoteSettings: {
          defaultParam: {},
        },
        algorithmRows: [],
        summaryData: [],
        columns: [
          {
            label: '股票名称',
            prop: 'instrumentName',
          },
          {
            label: '股票代码',
            prop: 'instrument',
          },
          {
            label: '汇总成交量',
            prop: 'summaryVolume',
          },
          {
            label: '成交均价',
            prop: 'avgPrice',
            render: (val) => {
              return val.toFixed(2);
            },
          },
          {
            label: '开盘价',
            prop: 'open',
          },
          {
            label: '成交金额',
            prop: 'amount',
            render: (val) => {
              return val.thousands();
            },
          },
        ],
        current: null,
      },
      watch: {
        current() {
          self.handleFilterChange();
        },
      },
      mounted() {
        this.getRemoteDefaultParam();
      },
      methods: {
        renderActiveClass(item) {
          if (!this.current) return '';
          return this.current.id == item.id ? 'active' : '';
        },
        handleActive(item) {
          console.log(item);
          if (item) {
            this.current = item;
          }
        },
        add() {
          this.algorithmDialog.item = null;
          this.algorithmDialog.visible = true;
        },
        setting() {
          this.defaultParamDialog.visible = true;
        },
        getItemClass(status) {
          if (TaskObject.isTaskRunning(status)) {
            return 'el-icon-video-pause';
          } else if (TaskObject.isTaskPaused(status)) {
            return 'el-icon-video-play';
          } else if (TaskObject.isTaskFinished(status)) {
            return 'el-icon-check';
          } else if (TaskObject.isTaskCreated(status)) {
            return 'el-icon-video-play';
          }
          return '';
        },
        getItemStatusClass(status) {
          if (TaskObject.isTaskRunning(status)) {
            return 'pause';
          } else if (TaskObject.isTaskPaused(status)) {
            return 'play';
          } else if (TaskObject.isTaskFinished(status)) {
            return 'check';
          } else if (TaskObject.isTaskCreated(status)) {
            return 'create';
          }
          return '';
        },
        switchStatus(item) {
          let status = item.strikeBoardStatus;
          if (TaskObject.isTaskRunning(status)) {
            self.renderProcess.send(self.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: item.id });
            self.interaction.showSuccess('已发送【停止】指令');
          } else if (TaskObject.isTaskPaused(status) || TaskObject.isTaskCreated(status)) {
            let cloned = self.helper.deepClone(item);
            delete cloned.rows;
            self.renderProcess.send(self.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.start, cloned, 0);
            self.interaction.showSuccess('已发送【开始】指令');
          }
        },
        renderStatus(status) {
          let matched = TaskStatuses.find((x) => x.value == status);
          return matched ? matched.label : status;
        },
        viewParams(item) {
          this.algorithmDialog.item = item;
          this.algorithmDialog.visible = true;
        },
        cancel(item) {
          self.renderProcess.send(self.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: item.id });
          self.interaction.showSuccess('撤单请求已提交');
        },
        clone(item) {
          let cloned = self.helper.deepClone(item);
          cloned.id = null;
          this.algorithmDialog.item = cloned;
          this.algorithmDialog.visible = true;
        },
        deleteRow(item) {
          self.interaction.showConfirm({
            title: '操作确认',
            message: '是否删除该算法？',
            confirmed: () => {
              self.renderProcess.send(self.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.delete, { id: item.id });
              self.interaction.showSuccess('已发送【删除】指令');
            },
          });
        },
        shouldDisable(item) {
          return TaskObject.isTaskRunning(item.strikeBoardStatus);
        },
        shouldDisableCancel(item) {
          return TaskObject.isTaskCreated(item.strikeBoardStatus);
        },
        handleSave(form, paramForm, start) {
          // console.log(form, paramForm, start);
          const isLow = paramForm.positionPercent !== undefined;
          const isIntervally = form.algorithmType == algorithmTypeEnum.定时定量.value || form.algorithmType == algorithmTypeEnum.定时跟量.value;
          let item = this.algorithmDialog.item;
          const 定涨定量 = form.algorithmType == algorithmTypeEnum.定涨定量.value;
          // 确定交易方向
          let direction = self.systemTrdEnum.tradingDirection.sell.code;
          let task = new TaskObject({
            id: item ? item.id : null,
            userId: self.userInfo.userId,
            userName: self.userInfo.userName,
            instrument: form.instrument,
            instrumentName: null,
            direction: direction,
            priceFollowType: paramForm.priceFollowType,
            orderPrice: 0,
            limitPositionType: isLow ? 6 : 1,
            positionPercent: paramForm.positionPercent,
            strikeBoardStatus: item ? item.strikeBoardStatus : TaskStatus.created.value,
            supplementVolume: item ? item.supplementVolume : 0,
            supplementOpen: false,
            splitInterval: 0,
            splitType: 0,
            sellMode: 1,
            splitDetail: { enableMax: false, max: 0, decline: false },
            boardStrategy: {
              strategyTime: paramForm.strategyTime ? Number(paramForm.strategyTime) : undefined, // 定时卖的时间
              strategyType: form.algorithmType,
              strategyVolumeOpen: isIntervally,
              strategyVolume: paramForm.strategyVolume || 0,
              strategyAmount: paramForm.strategyAmount || 0,
              strategyRateOpen: false,
              strategyRate: paramForm.strategyRate,
              strategyDelayTime: 定涨定量 ? 1000 : paramForm.strategyDelayTime || 0,
              sellAmountOpen: false,
              sellAmount: 0,
              sellVolumeOpen: false,
              sellVolume: 0,
              raiseRateLimit: paramForm.raiseRateLimit,
              raiseOffset: paramForm.raiseOffset,
              priceOffset: paramForm.priceOffset,
              upLimit: paramForm.upLimit,
              downLimit: paramForm.downLimit,
              riskRate: paramForm.riskRate,
              protectPrice: paramForm.protectPrice,
              protectRate: paramForm.protectRate,
              stopLossPrice: paramForm.stopLossPrice,
              stopLossRate: paramForm.stopLossRate,
              takeProfitPrice: paramForm.takeProfitPrice,
              takeProfitRate: paramForm.takeProfitRate,
              fixOrderAmount: paramForm.fixOrderAmount,
            },
            cancelCondition: {
              cancelProtectedEnabled: paramForm.cancelProtectedEnabled || false,
              cancelProtectedTime: paramForm.cancelProtectedTime,
            },
            cash: 0,
            creditFlag: false,
          });
          console.log('save task: ', task);
          function getCmdCode() {
            if (!item) {
              return start ? Cm20FunctionCodes.request.start : Cm20FunctionCodes.request.createSell;
            } else {
              if (item.id) {
                return Cm20FunctionCodes.request.modify;
              } else {
                return start ? Cm20FunctionCodes.request.start : Cm20FunctionCodes.request.createSell;
              }
            }
          }
          let cmdCode = getCmdCode();
          // console.log('cmdCode', cmdCode);
          self.log(`to start/stop/modify a task, task = ${JSON.stringify(task)}`);
          self.renderProcess.send(self.systemEvent.transmitMsg2TradingServer, cmdCode, task);
        },
        async handleDefaultParamSave(updatedDefaultParam) {
          let resp = await repo20Cm.setting(
            JSON.stringify({
              ...this.remoteSettings,
              defaultParam: updatedDefaultParam,
            }),
          );
          if (resp.errorCode == 0) {
            self.interaction.showSuccess('保存成功');
            Vue.set(this.remoteSettings, 'defaultParam', updatedDefaultParam);
          } else {
            self.interaction.showError('保存失败');
          }
        },
        async getRemoteDefaultParam() {
          var resp = await repo20Cm.querySetting();
          var content = (resp.data || {}).settings;
          try {
            if (typeof content == 'string' && content.length > 0) {
              let latest = JSON.parse(content);
              Vue.set(this, 'remoteSettings', {
                ...latest,
                defaultParam: latest.defaultParam || {},
              });
              // console.log('remoteSettings', this.remoteSettings);
            }
          } catch (error) {}
        },
        async updateTableData() {
          if (!this.current) {
            this.summaryData = [];
            return;
          }
          let current = this.current;
          let orders = [new Order({})].slice(1);
          orders = self.tableOrder.extractAllRecords();
          let matchedOrders = orders.filter((x) => x.parentOrderId == current.id);
          let summaryVolume = matchedOrders.map((x) => x.tradedVolume).sum();
          let amount = matchedOrders.map((x) => x.tradedAmount).sum();
          let avgPrice = summaryVolume == 0 ? 0 : amount / summaryVolume;

          if (!this.priceMap[current.instrument]) {
            await this.getPrice(current.instrument);
          }

          let row = {
            instrument: current.instrument,
            instrumentName: current.instrumentName,
            summaryVolume,
            amount,
            avgPrice,
            open: this.priceMap[current.instrument],
          };
          this.summaryData = [row];
        },
        refresh() {
          self.requestOrders();
        },
        async getPrice(instrument) {
          let resp = await repoInstrument.queryPrice(instrument);
          let { errorCode, errorMsg, data } = resp;
          // console.log(data);

          if (errorCode == 0 && data) {
            Vue.set(this.priceMap, instrument, data.preClosePrice);
          }
        },
      },
    });
  }

  handleNotify(records) {
    console.log('卖出算法任务: ', records);

    if (records.length == 0) {
      return;
    }
    records.sort((x, y) => {
      return x.id > y.id ? -1 : x.id < y.id ? 1 : 0;
    });
    let sells = records.filter((task) => {
      if (task.direction == this.systemTrdEnum.tradingDirection.sell.code) {
        return true;
      }
    });
    if (sells.length > 0) {
      let vueApp = this.vueApp;
      sells.forEach((task) => {
        let matched = vueApp.algorithmRows.find((x) => x.id == task.id);
        if (matched) {
          if (TaskObject.isTaskDeleted(task.strikeBoardStatus)) {
            vueApp.algorithmRows.splice(vueApp.algorithmRows.indexOf(matched), 1);
            if (task.id == vueApp.current.id) {
              vueApp.current = null;
            }
          } else {
            vueApp.algorithmRows.splice(vueApp.algorithmRows.indexOf(matched), 1, this.buildTaskRow(task));
          }
        } else {
          if (!TaskObject.isTaskDeleted(task.strikeBoardStatus)) {
            vueApp.algorithmRows.push(this.buildTaskRow(task));
          }
        }
      });
    }
  }

  /**
   * @param {TaskObject} task
   * @returns {TaskObject}
   */
  buildTaskRow(task) {
    // console.log(111, task);

    task.rows = [
      {
        vals: [
          {
            label: '股票名称：',
            value: task.instrumentName,
          },
          {
            label: '股票代码：',
            value: task.instrument,
          },
          {
            label: '计划总量：',
            value: task.targetVolume,
            formatter: (val) => {
              let formatted = (val || 0) / 100;
              return parseInt(formatted).thousands() + '手';
            },
          },
        ],
      },
      {
        vals: [
          {
            label: '算法名称：',
            value: task.boardStrategy.strategyType,
            formatter: (val) => {
              return Object.values(algorithmTypeEnum).find((x) => x.value == val).label;
            },
          },
          {
            label: '创建时间：',
            value: task.createTime,
            formatter: (val) => {
              return this.helper.time2String(val);
            },
          },
          {
            label: '完成总量：',
            value: task.tradedVolume,
            formatter: (val) => {
              let formatted = (val || 0) / 100;
              return parseInt(formatted).thousands() + '手';
            },
          },
        ],
      },
    ];
    return task;
  }

  listen2Notifies() {
    this.renderProcess.on(Cm20FunctionCodes.reply.queried.toString(), (event, { data, errorCode, errorMsg }, reqId) => {
      errorCode == 0 && this.handleNotify(data, reqId);
    });

    this.renderProcess.on(Cm20FunctionCodes.reply.created.toString(), (event, { data, errorCode, errorMsg }, reqId) => {
      errorCode == 0 && this.handleNotify([data], reqId);
    });

    const cnotify = Cm20FunctionCodes.notify;
    this.renderProcess.on(cnotify.created.toString(), (event, task, reqId) => {
      console.log('created');

      this.handleNotify([task], reqId);
    });
    this.renderProcess.on(cnotify.changed.toString(), (event, task, reqId) => {
      console.log('changed');

      this.handleNotify([task], reqId);
    });
    this.renderProcess.on(cnotify.deleted.toString(), (event, task, reqId) => {
      console.log('deleted');

      this.handleNotify([task], reqId);
    });
  }

  requestTasks() {
    this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.query, {});
  }

  /**
   * @param {Order} record
   */
  identifyOrder(record) {
    return record.id;
  }

  createTable() {
    return;
  }

  createOrder() {
    const $table = this.$container.querySelector('.table-order');
    const ref = new SmartTable($table, this.identifyOrder, this, {
      tableName: 'smt-20cm-july-order',
      displayName: '委托',
      defaultSorting: { prop: 'orderTime', direction: 'desc' },
      virtual: true,
      // rowDbClicked: this.handleOrderRowPunch.bind(this),
      headerHeight: 32,
      rowHeight: 32,
      footerHeight: 32,
    });

    ref.setPageSize(99999);
    this.tableOrder = ref;
  }

  async requestOrders() {
    var resp = await repoOrder.batchMemQuery({ trade_user_id: this.userInfo.userId });

    if (resp.errorCode != 0) {
      this.interaction.showError(`委托查询错误：${resp.errorCode}/${resp.errorMsg}`);
      return;
    }

    var records = resp.data;
    /** 首行数据，为标题栏 */
    var titles = records.shift();
    var orders = ModelConverter.formalizeOrders(titles, records);
    this.tableOrder.refill(orders);
    // console.log('orders', orders);

    this.mapNotifieds(orders);
    this.handleFilterChange();
  }

  /**
   * @param {Array<Order>} orders
   */
  mapNotifieds(orders) {
    var stses = this.systemEnum.orderStatus;
    orders.forEach((order) => {
      let tradedAll = order.orderStatus == stses.traded.code;
      let tradedPartial = order.orderStatus == stses.partialTraded.code;

      if (order.parentOrderId && (tradedAll || tradedPartial)) {
        this.notifyingMap[order.id] = true;
      }
    });
  }

  /**
   * @param {Order} record
   */
  formatActions(record) {
    return record.isCompleted ? '' : '<button class="danger" event.onclick="cancel" style="font-size:16px;">撤单</button>';
  }

  /**
   * 单一撤单
   * @param {Order} order
   */
  cancel(order) {
    this.confirm(false, `是否撤销：${order.instrumentName} ?`, () => {
      this.log(`to cancel an order: ${JSON.stringify(order)}`);
      this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: order.id });
      this.interaction.showSuccess(`${order.instrumentName}，撤单请求已发出`);
    });
  }

  /**
   * @param {*} struc
   */
  handleOrderChange(struc) {
    var order = new Order(struc);
    var is4Me = order.userId == this.userInfo.userId;
    var isAdjustPos = order.adjustFlag;
    console.log('order change', is4Me, isAdjustPos);

    if (!is4Me || isAdjustPos) {
      return;
    }

    this.log(`received an order change notify: ${JSON.stringify(struc)}`);
    this.tableOrder.putRow(order);
    this.ask2Notify(order);

    this.handleFilterChange();
  }

  /**
   * @param {Order} order
   */
  ask2Notify(order) {
    var stses = this.systemEnum.orderStatus;
    var dirs = this.systemTrdEnum.tradingDirection;
    var tradedAll = order.orderStatus == stses.traded.code;
    var tradedPartial = order.orderStatus == stses.partialTraded.code;
    var isBuy = order.direction == dirs.buy.code;
    var pid = order.parentOrderId;
    var isRequired = pid && (tradedAll || tradedPartial) && this.notifyingMap[pid] === undefined;

    if (!isRequired) {
      return;
    }

    this.notifyingMap[pid] = true;
    let directionLabel = isBuy ? '买入' : '卖出';
    this.interaction.notify({
      title: `${directionLabel}提示`,
      type: 'success',
      position: 'bottom-right',
      message: `${order.instrument}/${order.instrumentName}，已${isBuy ? '买入' : '卖出'}${order.tradedVolume}`,
    });

    this.ring4OrderFirstTrade(isBuy);
  }

  handleFilterChange() {
    this.tableOrder.customFilter((record) => {
      return this.testOrder(record);
    });
    this.vueApp.updateTableData();
  }

  /**
   * @param {Order} record
   */
  testOrder(record) {
    let isActiveAlgorithmRecord = this.vueApp.current && this.vueApp.current.id == record.parentOrderId;
    return !!isActiveAlgorithmRecord;
  }

  listen2OrderChange() {
    this.standardListen(this.serverEvent.orderChanged, this.handleOrderChange.bind(this));
  }

  subChange() {
    this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.orderChanged]);
  }

  unsubChange() {
    this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.orderChanged]);
  }

  dispose() {
    this.unsubChange();
  }

  /**
   * @param {HTMLElement} $container
   */
  async build($container) {
    super.build($container);
    await this.createView();
    this.createOrder();
    this.listen2Notifies();
    this.requestTasks();
    this.listen2OrderChange();
    this.subChange();
    this.requestOrders();
    this.thisWindow.on('resize', () => {
      this.vueApp.$refs.table.tableHeight++;
      setTimeout(() => {
        this.vueApp.$refs.table.tableHeight--;
      }, 100);
    });
  }
};
