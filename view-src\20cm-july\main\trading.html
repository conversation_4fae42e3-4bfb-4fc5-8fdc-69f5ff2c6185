<div class="view-layout">
  <div class="view-toolbar tabbed-radios-external s-pdl-10 s-pdr-10">
    <el-radio-group size="mini" v-model="states.focused" @change="handleModeChange">
      <el-radio-button :label="modes.mannual">手动交易</el-radio-button>
      <el-radio-button :label="modes.auto">自动算法</el-radio-button>
    </el-radio-group>
  </div>

  <div class="trading-panel s-full-height">
    <div v-show="isMannualMode()">
      <div class="input-row">
        <el-radio-group size="mini" v-model="mannual.direction" @change="handleDirectionChange">
          <el-radio-button :label="directions.buy">买入</el-radio-button>
          <el-radio-button :label="directions.sell">卖出</el-radio-button>
        </el-radio-group>
      </div>

      <div class="input-row">
        <label class="input-text">代码</label>
        <el-autocomplete
          placeholder="输入代码或名称"
          class="input-ctr"
          v-model="mannual.keywords"
          :fetch-suggestions="handleSuggest"
          @keydown.native="handleInput"
          @clear="handleClear"
          @select="handleSelect"
          prefix-icon="iconfont icon-sousuo"
          clearable
        >
          <template slot-scope="{ item: ins }">
            <span class="item-name">{{ ins.instrumentName }} </span>
            <span class="item-code">[{{ ins.instrument }}]</span>
          </template>
        </el-autocomplete>
      </div>

      <div class="input-row">
        <label class="input-text">价格</label>
        <el-input-number class="input-ctr concentrated-input" :step="decidePriceStep()" :min="0" :max="9999.99" v-model="mannual.price" @input.native="calculateByPrice($event)"></el-input-number>
      </div>

      <div class="input-row" style="margin-top: 5px; height: 15px; text-align: center; justify-content: space-between">
        <a class="link-btn s-pull-left s-color-green" @click="setAsPrice(mannual.lowerSellLimit)"
          >卖下限
          <span class="s-bold">{{ precisePrice(mannual.lowerSellLimit) }}</span>
        </a>
        <a class="link-btn s-pull-right s-color-red" @click="setAsPrice(mannual.upperBuyLimit)"
          >买上限
          <span class="s-bold">{{ precisePrice(mannual.upperBuyLimit) }}</span>
        </a>
      </div>

      <div class="input-row" style="margin-top: 5px; height: 15px; text-align: center; justify-content: space-between">
        <a class="link-btn s-pull-left s-color-green" @click="setAsPrice(mannual.floor)"
          >跌停
          <span class="s-bold">{{ precisePrice(mannual.floor) }}</span>
        </a>
        <span>{{ mannual.estimated }}</span>
        <a class="link-btn s-pull-right s-color-red" @click="setAsPrice(mannual.ceiling)"
          >涨停
          <span class="s-bold">{{ precisePrice(mannual.ceiling) }}</span>
        </a>
      </div>

      <div v-if="mannual.isByVolume" class="input-row">
        <el-tooltip content="点击切换到金额下单" placement="left">
          <label class="input-text" :class="mannual.direction == directions.buy ? 'by-volume-flag' : ''" @click="toggleMethod">数量</label>
        </el-tooltip>
        <div class="input-ctr with-unit concentrated-input">
          <el-input-number class="s-full-width" :step="100" :min="0" :max="9999999900" v-model="mannual.volume" @input.native="calculateByVolume($event)"></el-input-number>
          <span class="ctr-unit">股</span>
        </div>
      </div>

      <div v-else class="input-row">
        <el-tooltip content="点击切换到数量下单" placement="left">
          <label class="input-text by-amount-flag" @click="toggleMethod">金额</label>
        </el-tooltip>
        <div class="input-ctr with-unit concentrated-input">
          <el-input-number class="s-full-width" :step="100000" :min="0" :max="**********" v-model="mannual.amount" @input.native="calculateByAmount($event)"></el-input-number>
          <span class="ctr-unit">元</span>
        </div>
      </div>

      <div class="input-row ratios" style="margin-top: 15px; height: 25px">
        <el-tooltip placement="left">
          <div slot="content" v-html="isMannualBuy() ? getAccountAvailableCondition() : getPositionCondition()"></div>
          <a class="link-btn" @click="setByRatio(1)">全部</a>
        </el-tooltip>
        <a class="link-btn" @click="setByRatio(2)">1/2</a>
        <a class="link-btn" @click="setByRatio(3)">1/3</a>
        <a class="link-btn" @click="setByRatio(4)">1/4</a>
        <template v-if="!mannual.isEditingCustomRatio">
          <a class="link-btn" @click="setByCustomRatio(mannual.customRatio)">{{ mannual.customRatio }}%</a>
          <el-tooltip content="自定义百分比">
            <i class="el-icon-edit s-mgl-5 s-fs-16" @click="mannual.isEditingCustomRatio = true"></i>
          </el-tooltip>
        </template>
        <template v-else>
          <a class="custom-ratio">
            <el-input-number
              placeholder="百分比"
              :controls="false"
              :step="1"
              :min="1"
              :max="100"
              v-model="mannual.customRatio"
              @blur="handleCustomRatioBlur"
              @change="handleCustomRatioChange"
            ></el-input-number>
            <span>%</span>
          </a>
        </template>
      </div>

      <div class="input-row" style="margin-top: 7px">
        <div class="s-full-width" v-if="isMannualBuy()">
          <div class="s-full-width" v-if="states.isCreditAccount">
            <el-button size="small" type="danger" @click="mbuy" style="width: 47%">普通买入</el-button>
            <el-button size="small" type="danger" @click="mcredit" style="width: 47%">融资买入</el-button>
          </div>
          <el-button v-else size="small" class="s-full-width" type="danger" @click="mbuy">买入</el-button>
        </div>
        <el-button v-else size="small" class="s-full-width" type="success" @click="msell">卖出</el-button>
      </div>
    </div>

    <div v-show="isAutoMode()">
      <div class="input-row">
        <label class="input-text">代码</label>
        <el-autocomplete
          placeholder="输入代码或名称"
          class="input-ctr"
          v-model="auto.keywords"
          :fetch-suggestions="handleSuggest"
          @keydown.native="handleInput"
          @clear="handleClear"
          @select="handleSelect"
          prefix-icon="iconfont icon-sousuo"
          clearable
        >
          <template slot-scope="{ item: ins }">
            <span class="item-name">{{ ins.instrumentName }} </span>
            <span class="item-code">[{{ ins.instrument }}]</span>
          </template>
        </el-autocomplete>
      </div>

      <div class="input-row">
        <label class="input-text">策略</label>
        <el-select size="mini" :disabled="!isAsNewTask()" v-model="auto.strategy" class="input-ctr">
          <el-option v-for="(item, item_idx) in strategies" :key="item_idx" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>

      <!-- 定时定量买入 -->
      <div v-if="isIntervalVolumeBuy()">
        <div class="flex-row">
          <div class="input-row">
            <label class="input-text">间隔</label>
            <div class="input-ctr with-unit">
              <el-input-number :controls="false" class="s-full-width" :step="1" :min="1" :max="100" v-model="auto.strategyDelayTime"></el-input-number>
              <span class="ctr-unit">秒</span>
            </div>
          </div>

          <div class="input-row">
            <label class="input-text">档位</label>
            <el-select v-model="auto.level" class="input-ctr">
              <el-option v-for="(item, item_idx) in buyLevels" :key="item_idx" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>

        <div class="input-row">
          <div class="input-row">
            <label class="input-text">未成撤单</label>
            <div class="input-ctr concentrated-input">
              <el-checkbox v-model="auto.cancel.enabled"></el-checkbox>
              <el-input-number class="cancel-option-ctr" size="small" clearable :controls="false" :step="1" :min="0" v-model="auto.cancel.time"></el-input-number>
              <span class="ctr-unit">秒</span>
            </div>
          </div>

          <div class="input-row" style="min-width: 1px; flex: 1; margin-left: 8px">
            <label class="input-text">单笔</label>
            <div class="input-ctr with-unit concentrated-input single">
              <el-input-number class="s-full-width" :step="100" step-strictly :min="0" :max="9999999900" v-model="auto.volume"></el-input-number>
              <span class="ctr-unit">股</span>
            </div>
          </div>
        </div>

        <div class="input-row">
          <label class="input-text">总买入量</label>
          <div class="input-ctr with-unit total-buy">
            <el-input-number class="s-full-width" :step="100" step-strictly :min="0" :max="999999900" v-model="auto.targetVolume"></el-input-number>
            <span class="ctr-unit">股</span>
          </div>
        </div>

        <div class="flex-row">
          <div class="input-row">
            <label class="input-text">涨幅上限</label>
            <div class="input-ctr with-unit">
              <el-input-number :controls="false" class="s-full-width" placeholder="" :step="1" :min="-20" :max="20" v-model="auto.upperLimit"></el-input-number>
              <span class="ctr-unit">%</span>
            </div>
          </div>

          <div class="input-row">
            <label class="input-text">涨幅下限</label>
            <div class="input-ctr with-unit">
              <el-input-number :controls="false" class="s-full-width" placeholder="" :step="1" :min="-20" :max="20" v-model="auto.lowerLimit"></el-input-number>
              <span class="ctr-unit">%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 定时定量卖出 -->
      <div v-if="isIntervalVolumeSell()">
        <div class="flex-row">
          <div class="input-row">
            <label class="input-text">间隔</label>
            <div class="input-ctr with-unit interval-input">
              <el-input-number :controls="false" class="" :step="1" :min="1" :max="100" v-model="auto.strategyDelayTime"></el-input-number>
              <span class="ctr-unit">秒</span>
            </div>
          </div>

          <div class="input-row" style="margin-left: 20px">
            <label class="input-text">档位</label>
            <el-select v-model="auto.level" class="input-ctr">
              <el-option v-for="(item, item_idx) in levels" :key="item_idx" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>

        <div class="input-row">
          <label class="input-text">单笔数量</label>
          <div class="input-ctr with-unit concentrated-input">
            <el-input-number class="s-full-width" :step="100" step-strictly :min="0" :max="9999999900" v-model="auto.volume"></el-input-number>
            <span class="ctr-unit">股</span>
          </div>
        </div>

        <div class="flex-row">
          <div class="input-row">
            <label class="input-text">未成撤单</label>
            <div class="input-ctr concentrated-input">
              <el-checkbox v-model="auto.cancel.enabled"></el-checkbox>
              <el-input-number class="cancel-option-ctr" size="small" clearable :controls="false" :step="1" :min="0" v-model="auto.cancel.time"></el-input-number>
              <span class="ctr-unit">秒</span>
            </div>
          </div>

          <div class="input-row" style="margin-left: 4px">
            <label class="input-text">卖出仓位</label>
            <div class="input-ctr with-unit sell-position">
              <el-input-number :controls="false" class="s-full-width" placeholder="比例 1 ~ 100" :step="1" :min="1" :max="100" v-model="auto.positionPercent"></el-input-number>
              <span class="ctr-unit">%</span>
            </div>
          </div>
        </div>
        <div class="flex-row">
          <div class="input-row">
            <label class="input-text">涨幅上限</label>
            <div class="input-ctr with-unit">
              <el-input-number :controls="false" class="s-full-width" placeholder="" :step="1" :min="-20" :max="20" v-model="auto.upperLimit"></el-input-number>
              <span class="ctr-unit">%</span>
            </div>
          </div>

          <div class="input-row">
            <label class="input-text">涨幅下限</label>
            <div class="input-ctr with-unit">
              <el-input-number :controls="false" class="s-full-width" placeholder="" :step="1" :min="-20" :max="20" v-model="auto.lowerLimit"></el-input-number>
              <span class="ctr-unit">%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 低封单量 -->
      <div v-if="isLowBuy1VolumeStrategy()">
        <div class="input-row">
          <label class="input-text">比例</label>
          <div class="input-ctr with-unit">
            <el-input-number class="s-full-width" placeholder="比例 1 ~ 100" :step="1" :min="1" :max="100" v-model="auto.positionPercent"></el-input-number>
            <span class="ctr-unit">%</span>
          </div>
        </div>

        <div class="input-row">
          <label class="input-text">档位</label>
          <el-select v-model="auto.level" class="input-ctr">
            <el-option v-for="(item, item_idx) in levels" :key="item_idx" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>

        <div class="input-row">
          <label class="input-text">数量</label>
          <div class="input-ctr with-unit concentrated-input">
            <el-input-number class="s-full-width" :step="properStep" :min="0" :max="9999999900" v-model="auto.volume"></el-input-number>
            <span class="ctr-unit">手</span>
          </div>
        </div>
      </div>

      <!-- 定时买 -->
      <div v-if="isScheduledBuy()">
        <div class="input-row">
          <label class="input-text">时间</label>
          <el-time-picker class="input-ctr" v-model="auto.strategyTime" format="HH:mm:ss" value-format="HHmmss" placeholder="选择时间"> </el-time-picker>
        </div>

        <div class="input-row">
          <label class="input-text">价格</label>
          <div class="input-ctr with-unit">
            <el-input-number class="s-full-width" :step="0.01" :min="0" :max="9999.99" v-model="auto.orderPrice"></el-input-number>
            <span class="ctr-unit">元</span>
          </div>
        </div>

        <div v-if="auto.isByVolume" class="input-row">
          <el-tooltip content="点击切换到金额" placement="left">
            <label class="input-text by-volume-flag" @click="change2Amount">数量</label>
          </el-tooltip>
          <div class="input-ctr with-unit">
            <el-input-number class="s-full-width" :step="100" :min="0" :max="9999999900" v-model="auto.volume"></el-input-number>
            <span class="ctr-unit">股</span>
          </div>
        </div>

        <div v-else class="input-row">
          <el-tooltip content="点击切换到数量" placement="left">
            <label class="input-text by-amount-flag" @click="change2Volume">金额</label>
          </el-tooltip>
          <div class="input-ctr with-unit">
            <el-input-number class="s-full-width" :step="1000" :min="0" :max="**********" v-model="auto.orderAmount"></el-input-number>
            <span class="ctr-unit">元</span>
          </div>
        </div>

        <div class="input-row">
          <label class="input-text">未成撤单</label>
          <div class="input-ctr concentrated-input">
            <el-checkbox v-model="auto.cancel.enabled"></el-checkbox>
            <el-input-number class="cancel-option-ctr full-width" size="small" clearable :controls="false" :step="1" :min="0" v-model="auto.cancel.time"></el-input-number>
            <span class="ctr-unit">秒</span>
          </div>
        </div>
      </div>

      <div class="flex-row">
        <el-button :disabled="isAutoRunning()" size="small" type="primary" class="save-auto" @click="saveAuto">保存</el-button>

        <div v-if="isAutoRunning()" class="input-row trade-btn">
          <el-button size="small" type="warning" class="s-full-width" @click="stopAuto">暂停{{getAutoLabel()}}</el-button>
        </div>

        <div v-else class="input-row trade-btn">
          <el-button size="small" :type="(isIntervalVolumeBuy() || isScheduledBuy()) ? 'danger' : 'success'" class="s-full-width" @click="handleStart">开始{{getAutoLabel()}}</el-button>
        </div>
      </div>
    </div>
  </div>
</div>
