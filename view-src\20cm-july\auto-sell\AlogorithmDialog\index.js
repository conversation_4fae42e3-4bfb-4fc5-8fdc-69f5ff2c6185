const BaseComponent = require('../../../admin/indicator/components/BaseComponent');
const { <PERSON><PERSON><PERSON>elper } = require('../../../../libs/helper-biz');
const { repoPosition } = require('../../../../repository/position');
const { ModelConverter } = require('../../../../model/model-converter');
const { algorithmTypeEnum, fullParams } = require('../../../../config/auto-sell');

module.exports = class DragWidget extends BaseComponent {
  constructor() {
    super(__dirname);
    const self = this;
    this.options = {
      props: {
        visible: {
          required: true,
          type: <PERSON><PERSON>an,
        },
        item: {
          type: Object,
        },
        defaultSetting: {
          type: Object,
        },
      },
      data() {
        return {
          helper: self.parent.helper,
          positions: [],
          form: {
            instrument: '',
            instrumentName: '',
            algorithmType: '',
            canSellVolume: '',
          },
          rules: {
            instrumentName: [{ required: true, message: '请输入合约代码或名称', trigger: 'blur' }],
            algorithmType: [{ required: true, message: '请选择算法类型', trigger: 'change' }],
          },
          algorithmTypes: [algorithmTypeEnum.定时定量, algorithmTypeEnum.低封单量, algorithmTypeEnum.定时跟量, algorithmTypeEnum.定涨定量, algorithmTypeEnum.定时卖],
          paramForm: {},
          fullParams: fullParams,
        };
      },
      computed: {
        disableType() {
          return this.item && this.item.id ? true : false;
        },
        paramRules() {
          let rules = {};

          this.params.forEach((param) => {
            if (param.rules) {
              rules[param.prop] = param.rules;
            }
          });

          return rules;
        },
        title() {
          return this.item ? '查看参数' : '新建算法';
        },
        algorithmName() {
          let matched = this.algorithmTypes.find((x) => x.value == this.form.algorithmType);
          return matched ? matched.label : '';
        },
        params() {
          return this.fullParams[this.form.algorithmType];
        },
        displayParams() {
          return this.params.filter((x) => {
            if (x.show === false) {
              return false;
            }
            return true;
          });
        },
      },
      watch: {
        async visible(val) {
          if (val) {
            await this.requestPositions();
            if (this.item) {
              this.setData();
            }
          } else {
            this.resetData();
          }
        },
      },

      methods: {
        handleValueChange(item) {
          if (item.change) {
            item.change(this.paramForm);
          }
          // 当相关参数变化时，触发完成时间重新计算
          if (['strategyDelayTime', 'strategyRate', 'fixOrderAmount', 'positionPercent'].includes(item.prop)) {
            this.$forceUpdate();
          }
        },
        getDefaultParamValue(param) {
          let defaultParams = this.defaultSetting[this.form.algorithmType];
          // 保存在服务端的默认参数设置
          if (defaultParams && defaultParams[param.prop] !== undefined) {
            return defaultParams[param.prop];
          } else {
            if (param.default !== undefined) {
              return param.default;
            } else {
              return undefined;
            }
          }
        },
        handleSwitchProp(param) {
          param.show = false;
          this.paramForm[param.prop] = param.default === undefined ? undefined : 0;
          let alterParam = this.params.find((x) => x.alter == param.label);
          this.paramForm[alterParam.prop] = this.getDefaultParamValue(alterParam);
          alterParam.show = true;
        },
        setData() {
          console.log('回显参数：', this.item);
          this.form.instrument = this.item.instrument;
          this.form.instrumentName = this.item.instrumentName;
          this.form.algorithmType = this.item.boardStrategy.strategyType;
          this.handleChangeAlgorithmType();
          this.setCanSellVolume();
          this.params.forEach((param) => {
            if (param.isBoard) {
              let rawValue = this.item.boardStrategy[param.prop];
              // 回显切换属性时，显示正确的属性
              if (param.alter) {
                if (!rawValue) {
                  param.show = false;
                  let alterParam = this.params.find((x) => x.alter == param.label);
                  alterParam.show = true;
                }
              }
              if (rawValue === null || rawValue === undefined) {
                this.paramForm[param.prop] = undefined;
              } else {
                let raw = rawValue;
                if (param.times) {
                  raw = raw / param.times;
                }
                if (param.prop === 'strategyTime') {
                  raw = String(raw).padStart(6, '0');
                }

                this.paramForm[param.prop] = raw;
              }
            } else if (param.isCancel) {
              let rawValue = this.item.cancelCondition[param.prop];
              if (rawValue === null || rawValue === undefined || rawValue === 0) {
                this.paramForm[param.prop] = undefined;
              } else {
                this.paramForm[param.prop] = this.item.cancelCondition[param.prop];
              }
            } else {
              let rawValue = this.item[param.prop];
              if (rawValue === null || rawValue === undefined) {
                this.paramForm[param.prop] = undefined;
              } else {
                this.paramForm[param.prop] = this.item[param.prop];
              }
            }
          });
        },
        resetData() {
          this.$nextTick(async () => {
            this.form = {
              instrument: '',
              instrumentName: '',
              algorithmType: '',
              canSellVolume: '',
            };
            this.paramForm = {};
            this.positions = [];
            await self.parent.helper.sleep(200);
            if (this.$refs.paramForm) {
              this.$refs.paramForm.clearValidate();
            }
            this.$refs.form.clearValidate();
          });
        },
        handleChangeAlgorithmType() {
          this.initParamForm();
          this.$nextTick(() => {
            this.$refs.paramForm.clearValidate();
          });
        },
        initParamForm() {
          let form = {};
          this.params.forEach((item) => {
            if (item.alter) {
              if (item.show) {
                form[item.prop] = this.getDefaultParamValue(item);
              } else {
                form[item.prop] = item.default === undefined ? undefined : 0;
              }
            } else {
              form[item.prop] = this.getDefaultParamValue(item);
            }
          });
          Vue.set(this, 'paramForm', form);
          this.$nextTick(() => {
            this.updateCancelEnabled();
          });
        },
        updateCancelEnabled() {
          if (this.paramForm.cancelProtectedTime !== undefined) {
            this.paramForm.cancelProtectedEnabled = !!this.paramForm.cancelProtectedTime;
          }
        },
        handleSuggest(keywords, cb) {
          if (typeof keywords != 'string' || keywords.trim().length < 1) {
            cb(this.positions.filter(pos => pos.closableVolume > 0));
            return;
          }
          let matches = BizHelper.filterInstruments(self.parent.systemEnum.assetsType.stock.code, keywords);
          if (matches.length == 1) {
            cb([]);
            this.handleSelect(matches[0]);
            return;
          }

          cb(matches);
        },
        handleInput() {
          const ref = this.form;
          if (event.keyCode == 8) {
            event.returnValue = false;
            ref.instrumentName = null;
            this.handleClear();
          } else if (typeof ref.instrumentName == 'string' && ref.instrumentName.trim().length == 0) {
            this.handleClear();
          }
        },
        handleClear() {
          this.form.instrument = '';
          this.form.instrumentName = '';
          this.form.canSellVolume = '';
        },
        handleSelect(item) {
          var { instrument, instrumentName } = item;
          this.form.instrument = instrument;
          this.form.instrumentName = instrumentName;
          this.setCanSellVolume();
          this.$refs.form.clearValidate();
        },
        async setCanSellVolume() {
          if (!this.form.instrument) {
            this.form.canSellVolume = 0;
            return;
          }
          let matched = this.positions.find((x) => x.instrument == this.form.instrument);
          if (matched) {
            this.form.canSellVolume = Math.floor(matched.closableVolume / 100);
          } else {
            this.form.canSellVolume = 0;
          }
          // 当前股票没有持仓，提示用户
          if (this.form.canSellVolume == 0) {
            this.$message.warning(`当前股票无可卖出仓位`);
          }
        },
        async requestPositions() {
          var resp = await repoPosition.batchMemQuery({ trade_user_id: self.parent.userInfo.userId });
          if (resp.errorCode != 0) {
            return;
          }
          var records = resp.data;
          /** 首行数据，为标题栏 */
          var titles = records.shift();
          var positions = ModelConverter.formalizePositions(titles, records);
          this.positions.refill(positions);
        },
        cancel() {
          this.$emit('update:visible', false);
        },
        save() {
          this.$refs.form.validate((valid) => {
            if (valid) {
              this.$refs.paramForm.validate((valid) => {
                if (valid) {
                  // console.log('save', this.helper.deepClone(this.form), this.helper.deepClone(this.paramForm));
                  this.readySave();
                  this.cancel();
                }
              });
            }
          });
        },
        saveStart() {
          this.$refs.form.validate((valid) => {
            if (valid) {
              this.$refs.paramForm.validate((valid) => {
                if (valid) {
                  if (this.defaultSetting.confirmTwice === false) {
                    // console.log('save and start', this.form, this.paramForm);
                    this.readySave(true);
                    this.cancel();
                  } else {
                    this.$confirm(`是否启动卖出${this.form.instrument}/${this.form.instrumentName}${this.calcSellVolume()}手的${this.algorithmName}策略`, '提示', {
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                      type: 'warning',
                    }).then(() => {
                      // console.log('save and start', this.form, this.paramForm);
                      this.readySave(true);
                      this.cancel();
                    });
                  }
                }
              });
            }
          });
        },
        calcSellVolume() {
          // 手
          let canSellVolume = this.form.canSellVolume || 0;
          // 100%
          let percent = this.paramForm.positionPercent || 0;
          return Math.ceil((canSellVolume * percent) / 100);
        },
        getDisplayValue(item) {
          if (item.prop === 'estimatedTime') {
            return this.calculateEstimatedTime();
          }
          return '';
        },
        calculateEstimatedTime() {
          // 计算预估完成时间
          const { strategyDelayTime, strategyRate, fixOrderAmount, positionPercent } = this.paramForm;
          const canSellVolume = this.form.canSellVolume || 0;

          if (!strategyDelayTime) {
            return '请填写时间间隔';
          }

          if (!positionPercent) {
            return '请填写卖出仓位';
          }

          if (canSellVolume === 0) {
            return '无可卖持仓';
          }

          if (strategyRate === 0 && fixOrderAmount === 0) {
            return '请填写单笔仓位或单笔金额';
          }

          // 总卖出手数
          const totalSellVolume = Math.ceil((canSellVolume * positionPercent) / 100);
          console.log('canSellVolume', canSellVolume);
          console.log('positionPercent', positionPercent);
          console.log('totalSellVolume', totalSellVolume);
          let singleSellVolume = 0;

          // 根据单笔仓位或单笔金额计算单次卖出手数
          if (strategyRate && strategyRate > 0) {
            // 使用单笔仓位
            singleSellVolume = Math.ceil((canSellVolume * strategyRate) / 100);
          } else if (fixOrderAmount && fixOrderAmount > 0) {
            // 使用单笔金额，需要根据当前价格估算
            const matched = BizHelper.pick(this.form.instrument);
            if (!matched) {
              return '';
            }
            const estimatedPrice = matched.upperLimitPrice; // 按涨停价计算
            singleSellVolume = Math.floor((fixOrderAmount * 10000) / (estimatedPrice * 100));
          }

          // 单笔卖出数量不能小于1
          if (singleSellVolume < 1) {
            singleSellVolume = 1;
          }

          console.log('singleSellVolume', singleSellVolume);

          // 计算需要的次数
          const sellTimes = Math.ceil(totalSellVolume / singleSellVolume);

          console.log('sellTimes', sellTimes);

          // 计算总时间（秒）
          const totalSeconds = sellTimes * strategyDelayTime;

          console.log('totalSeconds', totalSeconds);

          // 转换为小时、分钟和秒
          const hours = Math.floor(totalSeconds / 3600);
          const minutes = Math.floor((totalSeconds % 3600) / 60);
          const seconds = totalSeconds % 60;

          if (hours > 0) {
            return `${hours}小时${minutes}分${seconds}秒`;
          } else if (minutes > 0) {
            return `${minutes}分${seconds}秒`;
          } else {
            return `${seconds}秒`;
          }
        },
        readySave(start) {
          let paramForm = this.helper.deepClone(this.paramForm);
          this.params.forEach((param) => {
            if (param.times) {
              paramForm[param.prop] = param.times * paramForm[param.prop];
            }
          });
          this.$emit('save', this.form, paramForm, start);
        },
      },
    };
  }
};
